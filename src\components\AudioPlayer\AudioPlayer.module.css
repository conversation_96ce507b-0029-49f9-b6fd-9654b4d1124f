.audioPlayer {
  width: 100%;
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin: 20px 0;
  border: 1px solid #f0f0f0;
  transition: box-shadow 0.2s ease;
}

.audioPlayer:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.audioTitle {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.playButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.playButton:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: scale(1.08);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.playButton:disabled {
  background: #e0e0e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progressContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progressBar {
  position: relative;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  cursor: pointer;
  transition: height 0.2s ease;
  overflow: hidden;
}

.progressBar:hover {
  height: 8px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.1s ease;
}

.progressHandle {
  position: absolute;
  top: 50%;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
}

.progressBar:hover .progressHandle {
  opacity: 1;
}

.timeDisplay {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
  font-family: 'Courier New', monospace;
}

.currentTime {
  font-weight: 500;
}

.separator {
  color: #999;
}

.duration {
  color: #999;
}

.volumeContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.volumeIcon {
  color: #666;
  flex-shrink: 0;
}

.volumeSlider {
  width: 80px;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volumeSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.volumeSlider::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.error {
  color: #dc3545;
  font-size: 12px;
  text-align: center;
  margin-top: 8px;
}

/* Variants */
.compact {
  padding: 12px;
  margin: 8px 0;
}

.compact .playButton {
  width: 36px;
  height: 36px;
}

.compact .volumeContainer {
  display: none;
}

.inline {
  background: transparent;
  box-shadow: none;
  padding: 8px 0;
  margin: 8px 0;
}

.inline .controls {
  gap: 12px;
}

.inline .playButton {
  width: 32px;
  height: 32px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .audioPlayer {
    padding: 16px;
    margin: 16px 0;
  }

  .controls {
    gap: 12px;
  }

  .playButton {
    width: 44px;
    height: 44px;
  }

  .volumeContainer {
    display: none;
  }

  .progressContainer {
    flex: 1;
  }

  .timeDisplay {
    font-size: 11px;
  }

  .progressBar {
    height: 8px;
  }

  .progressBar:hover {
    height: 10px;
  }
}

@media (max-width: 480px) {
  .audioPlayer {
    padding: 12px;
    margin: 12px 0;
    border-radius: 8px;
  }

  .controls {
    gap: 10px;
  }

  .playButton {
    width: 40px;
    height: 40px;
  }

  .progressBar {
    height: 10px;
  }

  .progressBar:hover {
    height: 12px;
  }

  .timeDisplay {
    font-size: 10px;
    gap: 2px;
  }

  .progressHandle {
    width: 18px;
    height: 18px;
  }
}
