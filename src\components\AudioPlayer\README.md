# AudioPlayer Component

A responsive audio player component for blog posts that integrates with Strapi CMS audio files.

## Features

- **Play/Pause Controls**: Clean play/pause button with loading states
- **Progress Bar**: Interactive progress bar with time display
- **Volume Control**: Volume slider (hidden on mobile for space)
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Multiple Variants**: Default, compact, and inline variants
- **Strapi Integration**: Seamlessly works with Strapi audio_file field

## Usage

### Basic Usage

```tsx
import AudioPlayer from '@components/AudioPlayer';

<AudioPlayer
  audioFile={blogData.audio_file}
  title="Listen to this blog post"
  variant="default"
  showTitle={true}
/>
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `audioFile` | `{data?: AudioFileData}` | - | Audio file data from Strapi |
| `title` | `string` | - | Title to display above player |
| `className` | `string` | `''` | Additional CSS classes |
| `variant` | `'default' \| 'compact' \| 'inline'` | `'default'` | Player size variant |
| `autoPlay` | `boolean` | `false` | Auto-play audio on load |
| `showTitle` | `boolean` | `true` | Show/hide title above player |

### Variants

- **default**: Full-featured player with all controls
- **compact**: Smaller player without volume control
- **inline**: Minimal player for blog cards

## Integration Points

### Blog Detail Pages
- Added to `BlogHeroSection` component
- Displays below blog description
- Uses `default` variant on desktop, `compact` on mobile

### Blog Listing Pages
- Added to `BlogListing` component
- Displays in blog cards
- Uses `inline` variant
- Click events are prevented from triggering navigation

## Strapi Configuration

Ensure your Strapi blog content type includes an `audio_file` field:

```javascript
// In your Strapi blog content type
audio_file: {
  type: 'media',
  multiple: false,
  required: false,
  allowedTypes: ['audio']
}
```

## Styling

The component uses CSS Modules with responsive breakpoints:

- **Desktop**: Full controls with volume slider
- **Tablet**: Compact controls, hidden volume
- **Mobile**: Touch-optimized controls, larger touch targets

## Browser Support

- Modern browsers with HTML5 audio support
- Progressive enhancement for older browsers
- Graceful fallback when audio files are unavailable

## Testing

To test the audio player:

1. **Development Server**: Run `npm run dev`
2. **Blog Pages**: Navigate to any blog post with audio_file data
3. **Responsive**: Test on different screen sizes
4. **Audio Controls**: Verify play/pause, progress, and volume work
5. **Blog Listing**: Check audio players in blog cards don't interfere with navigation

## Accessibility

- ARIA labels for all interactive elements
- Keyboard navigation support
- Screen reader compatible
- High contrast mode support
